{"provider": "autogen_agentchat.teams.SelectorGroupChat", "component_type": "team", "version": 1, "component_version": 1, "description": "A comprehensive team of 8 specialized agents for building Python AI applications with FastAPI, RAG, vector analysis, SMS integration, and Supabase backend - including architecture design, development, testing, and deployment specialists.", "label": "AI_Application_Development_Team", "config": {"participants": [{"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "label": "AugmentPlanner", "description": "Simulated Augment-style task planner", "config": {"name": "augment_planner", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "config": {"model": "gpt-4o", "temperature": 0.3, "api_key": "********************************************************************************************************************************************************************"}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "config": {}}, "system_message": "You are a senior planner simulating Augment AI behavior. Break large tasks into atomic subtasks, prioritize them, and delegate to appropriate agents in the team. Assign tasks like fastapi_developer, vector_expert, sms_integration_specialist, etc. Be precise, efficient, and make sure every subgoal is realistic and self-contained."}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Lead architect specializing in AI application design and system architecture", "label": "AI_Architect", "config": {"name": "ai_architect", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.3, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": [{"provider": "autogen_core.tools.FunctionTool", "component_type": "tool", "version": 1, "component_version": 1, "description": "Create architecture diagrams and system designs", "label": "ArchitectureTool", "config": {"source_code": "async def create_architecture_diagram(components: List[str], connections: List[Dict[str, str]], output_format: str = 'mermaid') -> str:\n    \"\"\"Create system architecture diagrams\n    \n    Args:\n        components: List of system components\n        connections: List of connections between components\n        output_format: Output format (mermaid, plantuml)\n    \n    Returns:\n        str: Architecture diagram in specified format\n    \"\"\"\n    if output_format == 'mermaid':\n        diagram = 'graph TD\\n'\n        for component in components:\n            diagram += f'    {component.replace(\" \", \"_\")}[\"{component}\"]\\n'\n        for conn in connections:\n            source = conn['from'].replace(' ', '_')\n            target = conn['to'].replace(' ', '_')\n            label = conn.get('label', '')\n            diagram += f'    {source} --> {target}'\n            if label:\n                diagram += f' : {label}'\n            diagram += '\\n'\n        return diagram\n    return 'Architecture diagram created'", "name": "create_architecture_diagram", "description": "Create system architecture diagrams for AI applications", "global_imports": [{"module": "typing", "imports": ["List", "Dict"]}], "has_cancellation_support": false}}]}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "AI Architecture Specialist - designs scalable AI application architectures", "system_message": "You are an AI Architecture Specialist with expertise in designing scalable AI applications. Your responsibilities include:\n1. Design overall system architecture for AI applications\n2. Plan integration patterns for FastAPI, RAG, vector databases, and external services\n3. Define data flow and component interactions\n4. Ensure scalability, security, and performance considerations\n5. Create technical specifications and architecture diagrams\n6. Guide technology stack decisions\n7. Plan deployment and infrastructure requirements\nFocus on microservices architecture, cloud-native solutions, and best practices for AI/ML applications.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "FastAPI and backend development specialist", "label": "FastAPI_Developer", "config": {"name": "fastapi_developer", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.2, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "FastAPI Backend Development Specialist", "system_message": "You are a FastAPI Backend Development Specialist. Your expertise includes:\n1. Building high-performance FastAPI applications\n2. Implementing RESTful APIs with proper documentation\n3. Database integration with Supabase and PostgreSQL\n4. Authentication and authorization (JWT, OAuth2)\n5. Middleware implementation and request/response handling\n6. API versioning and rate limiting\n7. WebSocket implementation for real-time features\n8. Integration with external services and APIs\n9. Performance optimization and caching strategies\nFocus on clean, maintainable code following FastAPI best practices.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Supabase and database specialist", "label": "Supabase_Specialist", "config": {"name": "supabase_specialist", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.2, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "workbench": {"provider": "autogen_core.tools.StaticWorkbench", "component_type": "workbench", "version": 1, "component_version": 1, "description": "A workbench that provides a static set of tools that do not change after each tool execution.", "label": "StaticWorkbench", "config": {"tools": [{"provider": "autogen_core.tools.FunctionTool", "component_type": "tool", "version": 1, "component_version": 1, "description": "Generate Supabase database schema with vector support and RLS", "label": "SupabaseSchemaGenerator", "config": {"source_code": "async def generate_supabase_schema(tables: List[Dict[str, Any]], enable_vector: bool = True) -> str:\n    \"\"\"Generate Supabase database schema with vector support\n    \n    Args:\n        tables: List of table definitions\n        enable_vector: Enable pgvector extension for vector operations\n    \n    Returns:\n        str: SQL schema for Supabase\n    \"\"\"\n    schema_sql = \"-- Supabase Database Schema\\n\\n\"\n    \n    if enable_vector:\n        schema_sql += \"-- Enable pgvector extension\\nCREATE EXTENSION IF NOT EXISTS vector;\\n\\n\"\n    \n    # Add RLS and security\n    schema_sql += \"-- Enable Row Level Security\\nALTER DATABASE postgres SET row_security = on;\\n\\n\"\n    \n    for table in tables:\n        table_name = table.get('name', 'unknown_table')\n        columns = table.get('columns', [])\n        \n        schema_sql += f\"-- Create {table_name} table\\n\"\n        schema_sql += f\"CREATE TABLE IF NOT EXISTS {table_name} (\\n\"\n        \n        # Add default columns\n        schema_sql += \"    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\\n\"\n        schema_sql += \"    created_at TIMESTAMPTZ DEFAULT NOW(),\\n\"\n        schema_sql += \"    updated_at TIMESTAMPTZ DEFAULT NOW(),\\n\"\n        \n        # Add custom columns\n        for col in columns:\n            col_name = col.get('name')\n            col_type = col.get('type', 'TEXT')\n            col_constraints = col.get('constraints', '')\n            schema_sql += f\"    {col_name} {col_type} {col_constraints},\\n\"\n        \n        # Add vector column if needed\n        if enable_vector and table.get('has_vector', False):\n            schema_sql += \"    embedding vector(1536),\\n\"\n        \n        schema_sql = schema_sql.rstrip(',\\n') + \"\\n);\\n\\n\"\n        \n        # Add indexes\n        if 'indexes' in table:\n            for index in table['indexes']:\n                schema_sql += f\"CREATE INDEX IF NOT EXISTS idx_{table_name}_{index} ON {table_name}({index});\\n\"\n        \n        # Add vector index if applicable\n        if enable_vector and table.get('has_vector', False):\n            schema_sql += f\"CREATE INDEX IF NOT EXISTS idx_{table_name}_embedding ON {table_name} USING ivfflat (embedding vector_cosine_ops);\\n\"\n        \n        # Add RLS policies\n        schema_sql += f\"ALTER TABLE {table_name} ENABLE ROW LEVEL SECURITY;\\n\"\n        schema_sql += f\"CREATE POLICY \\\"Enable read access for authenticated users\\\" ON {table_name} FOR SELECT USING (auth.role() = 'authenticated');\\n\\n\"\n        \n        # Add updated_at trigger\n        schema_sql += f\"\"\"-- Create updated_at trigger for {table_name}\nCREATE OR REPLACE FUNCTION update_updated_at_column()\nRETURNS TRIGGER AS $$\nBEGIN\n    NEW.updated_at = NOW();\n    RETURN NEW;\nEND;\n$$ language 'plpgsql';\n\nCREATE TRIGGER update_{table_name}_updated_at BEFORE UPDATE ON {table_name} FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();\n\n\"\"\"\n    \n    return schema_sql", "name": "generate_supabase_schema", "description": "Generate Supabase database schema with vector support and RLS", "global_imports": [{"module": "typing", "imports": ["List", "Dict", "Any"]}], "has_cancellation_support": false}}]}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Supabase and Database Specialist", "system_message": "You are a Supabase and Database Specialist. Your expertise includes:\n1. Supabase setup, configuration, and optimization\n2. PostgreSQL database design and performance tuning\n3. pgvector extension for vector storage and similarity search\n4. Row Level Security (RLS) and authentication policies\n5. Real-time subscriptions and database triggers\n6. Supabase Auth integration and user management\n7. Edge Functions and serverless database operations\n8. Database migrations and schema management\n9. Backup, recovery, and monitoring strategies\n10. Integration with Python applications using supabase-py\nFocus on scalable, secure database architectures optimized for AI applications.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "RAG and AI integration specialist", "label": "RAG_AI_Specialist", "config": {"name": "rag_ai_specialist", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.2, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "RAG and AI Integration Specialist", "system_message": "You are a RAG (Retrieval-Augmented Generation) and AI Integration Specialist. Your expertise includes:\n1. Designing and implementing RAG pipelines for intelligent document retrieval\n2. LLM integration with OpenAI, Anthropic, and other AI models\n3. Embedding strategies and semantic search optimization\n4. Context management and prompt engineering\n5. AI model fine-tuning and optimization\n6. Knowledge base construction and maintenance\n7. Intelligent FAQ systems and chatbot development\n8. AI-powered content generation and summarization\n9. Multi-modal AI integration (text, image, audio)\n10. AI model evaluation and performance monitoring\nFocus on creating intelligent, context-aware AI systems that provide accurate and relevant responses.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Vector database and similarity search expert", "label": "Vector_Expert", "config": {"name": "vector_expert", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.2, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Vector Database and Similarity Search Expert", "system_message": "You are a Vector Database and Similarity Search Expert. Your expertise includes:\n1. Vector database design and optimization (Pinecone, Weaviate, Chroma, pgvector)\n2. Embedding generation and management strategies\n3. Similarity search algorithms and optimization\n4. Document chunking and preprocessing techniques\n5. Vector indexing and retrieval performance tuning\n6. Multi-dimensional vector operations and analytics\n7. Hybrid search combining vector and traditional search\n8. Vector database scaling and distributed architectures\n9. Embedding model selection and evaluation\n10. Real-time vector updates and synchronization\nFocus on building high-performance vector search systems for AI applications.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "SMS integration and external API specialist", "label": "SMS_Integration_Specialist", "config": {"name": "sms_integration_specialist", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.2, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "SMS Integration and External API Specialist", "system_message": "You are an SMS Integration and External API Specialist. Your expertise includes:\n1. SMS service integration (Twilio, Kudocity, AWS SNS, etc.)\n2. Webhook implementation and management\n3. External API integration and authentication\n4. Message queuing and delivery optimization\n5. SMS template management and personalization\n6. Bulk messaging and campaign management\n7. SMS analytics and delivery tracking\n8. Multi-channel communication integration\n9. API rate limiting and error handling\n10. Compliance with SMS regulations and best practices\nFocus on reliable, scalable communication systems with proper error handling and monitoring.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "Quality assurance and testing specialist", "label": "QA_Tester", "config": {"name": "qa_tester", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.1, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "UnboundedChatCompletionContext", "config": {}}, "description": "Quality Assurance and Testing Specialist", "system_message": "You are a Quality Assurance and Testing Specialist. Your expertise includes:\n1. Comprehensive test suite design and implementation\n2. Unit testing, integration testing, and end-to-end testing\n3. API testing and validation (Postman, pytest, etc.)\n4. Performance testing and load testing\n5. Security testing and vulnerability assessment\n6. Test automation and CI/CD integration\n7. Bug tracking and quality metrics\n8. Code review and quality assurance processes\n9. User acceptance testing coordination\n10. Testing documentation and best practices\nFocus on ensuring high-quality, reliable software through thorough testing strategies.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}, {"provider": "autogen_agentchat.agents.AssistantAgent", "component_type": "agent", "version": 1, "component_version": 1, "description": "DevOps and deployment specialist", "label": "DevOps_Specialist", "config": {"name": "devops_specialist", "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.2, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "model_context": {"provider": "autogen_core.model_context.UnboundedChatCompletionContext", "component_type": "chat_completion_context", "version": 1, "component_version": 1, "description": "An unbounded chat completion context that keeps a view of all messages.", "label": "Unb oundedChatCompletionContext", "config": {}}, "description": "DevOps and Deployment Specialist", "system_message": "You are a DevOps and Deployment Specialist. Your expertise includes:\n1. CI/CD pipeline design and implementation\n2. Containerization with Docker and orchestration with Kubernetes\n3. Cloud deployment (AWS, GCP, Azure, Digital Ocean)\n4. Infrastructure as Code (Terraform, CloudFormation)\n5. Monitoring and logging (Prometheus, Grafana, ELK stack)\n6. Security and compliance in deployment pipelines\n7. Performance optimization and scaling strategies\n8. Backup and disaster recovery planning\n9. Environment management (dev, staging, production)\n10. Automated deployment and rollback strategies\nFocus on reliable, scalable, and secure deployment solutions with proper monitoring and maintenance.", "model_client_stream": false, "reflect_on_tool_use": false, "tool_call_summary_format": "{result}", "metadata": {}}}], "model_client": {"provider": "autogen_ext.models.openai.OpenAIChatCompletionClient", "component_type": "model", "version": 1, "component_version": 1, "description": "Chat completion client for OpenAI hosted models.", "label": "OpenAIChatCompletionClient", "config": {"temperature": 0.3, "model": "gpt-4o", "api_key": "********************************************************************************************************************************************************************"}}, "termination_condition": {"provider": "autogen_agentchat.base.OrTerminationCondition", "component_type": "termination", "version": 1, "component_version": 1, "label": "OrTerminationCondition", "config": {"conditions": [{"provider": "autogen_agentchat.conditions.TextMentionTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation if a specific text is mentioned.", "label": "TextMentionTermination", "config": {"text": "TERMINATE"}}, {"provider": "autogen_agentchat.conditions.MaxMessageTermination", "component_type": "termination", "version": 1, "component_version": 1, "description": "Terminate the conversation after a maximum number of messages have been exchanged.", "label": "MaxMessageTermination", "config": {"max_messages": 50, "include_agent_event": false}}]}}, "selector_prompt": "Always begin with augment_planner to decompose tasks.\n\nYou are coordinating an AI application development team by selecting the team member to speak/act next. The following specialized roles are available:\\n    {roles}\\n\\n    **Role Descriptions:**\\n    - ai_architect: Designs system architecture, plans integrations, creates technical specifications\\n    - fastapi_developer: Builds FastAPI backend, implements APIs, handles authentication and middleware\\n    - rag_ai_specialist: Implements RAG pipelines, LLM integration, embedding strategies\\n    - vector_expert: Manages vector databases, document analysis, similarity search optimization\\n    - sms_integration_specialist: Handles SMS services (Kudocity), webhooks, external API integrations\\n    - supabase_specialist: Manages Supabase setup, PostgreSQL optimization, pgvector, RLS policies\\n    - qa_tester: Creates comprehensive test suites, handles quality assurance, performance testing\\n    - devops_specialist: Manages deployment, CI/CD, containerization, infrastructure\\n\\n    **Selection Strategy:**\\n    1. **Planning Phase**: Start with ai_architect for system design\\n    2. **Development Phase**: Rotate between specialists based on current task\\n    3. **Integration Phase**: Coordinate between related specialists\\n    4. **Testing Phase**: Engage qa_tester for validation\\n    5. **Deployment Phase**: Engage devops_specialist for production readiness\\n\\n    **Guidelines:**\\n    - Select ai_architect for high-level design decisions and architecture planning\\n    - Choose technical specialists when working on their domain-specific components\\n    - Engage qa_tester when code/features need testing or validation\\n    - Select devops_specialist for deployment, CI/CD, and infrastructure concerns\\n    - Consider dependencies between components when selecting specialists\\n    - Ensure proper handoffs between related specialists (e.g., rag_ai_specialist → vector_expert)\\n\\n    Based on the conversation context and current development needs, select the most appropriate specialist.\\n\\n    {history}\\n\\n    Read the above conversation. Then select the next role from {participants} to play. ONLY RETURN THE ROLE.", "allow_repeated_speaker": true, "max_selector_attempts": 3, "emit_team_events": false, "model_client_streaming": false}}